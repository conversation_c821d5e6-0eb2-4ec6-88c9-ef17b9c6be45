#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多步骤合规审计系统配置文件
"""

# 继承原有API配置
from config import API_CONFIG, MODEL_CONFIG

# 多阶段流程配置
MULTI_STAGE_CONFIG = {
    "enable_multi_stage": True,
    "save_intermediate_results": True,
    "stage_delay": 1,  # 每个阶段之间的延迟（秒）
    "max_retries": 3,  # 每个阶段的最大重试次数
    "output_folder": "多阶段合规审查结果",
    "intermediate_folder": "中间结果"
}

# 问题类型定义 - 基于语义理解的分类
QUESTION_TYPES = {
    "DEFINITION": {
        "name": "定义/解释问题"
    },
    "OPERATION": {
        "name": "操作问题"
    },
    "YES_NO": {
        "name": "是/否问题"
    },
    "FACTUAL": {
        "name": "趋势性问题"
    },
    "OTHER": {
        "name": "其他类问题"
    }
}

# 第一阶段：合理性检查提示词
STAGE1_REASONABLENESS_PROMPT = """你是银行客服问答对合规预审专家，熟悉微众银行的业务背景、客户常见提问方式、银行操作规范与相关法律法规。

请对【问答对】进行**第一阶段预审判断**，识别以下两类不合格情况，并进行明确判断：

---
【任务目标】

提前识别并剔除明显不合规或无效问答对，避免其进入后续审核流程。判断依据包括：
1. 问题本身是否合理
2. 回答是否存在严重违反合规底线的表达（如违法承诺、伪造引导等）

---
【判断标准】

**1. 业务合理性判断**  
判断客户问题是否属于银行业务场景中常见或可能出现的问题，具有实际业务含义或操作价值。  
合理的例子包括但不限于：账户管理、合同签署、优惠券发放、APP操作流程、票据业务、贷款申请、资料提交等  
以下属于不合理：
- 与银行业务完全无关的内容（如：“你爱我吗”、“今天天气如何”、“你是不是机器人”）
- 明显恶意、测试性、虚构性提问

请注意：即使问题涉及“上传发票”、“微闪贴”、“888优惠券”等具体名词，也可能属于微众银行的真实业务范围，**不应以“传统银行经验”判断其为不合理**

**2. 明显违反合规底线判断（仅限严重情况）**  
判断回答中是否存在**严重违反法律法规、银行内部规范或业务底线**的内容，包括但不限于：
- 错误适配产品对象（如鼓励不合资格客户办理贷款）
- 强制免责、混淆产品信息、故意模糊表达（如“稳赚”“无风险”）
- 明确指导客户伪造材料、他人代签、非本人操作签约等高风险行为

提醒：  
- 如回答仅存在信息不全、语义模糊、缺少数据依据等轻微问题，请**允许其进入下一阶段**处理，不应在第一阶段直接剔除。
- 不允许以“回答不完美”为理由否定一个**原本合理的问题**。

---
【输出格式要求】（请使用 JSON 格式，字段值仅限下列值）

```json
{
  "reasonableness_judgment": "合理/不合理",
  "reasoning": "判断理由（简洁明确，不超过100字）",
  "stage1_result": "通过/不通过"
}

下面是多对推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：
问题：如果我补充资料后还是显示待对方应答怎么办?
回答：请您再次确认上传的资料是否齐全且准确，如果有遗漏或错误，请及时补充，如果仍有问题，建议您联系我们的客服进一步咨询
审核判断：通过
原因：客户关于补充资料后仍显示待对方应答的问题是银行业务中常见的客户疑问，符合微众银行的业务场景。
反例：
问题：客户是否有办法在没有新发票的情况下继续开票?
回答：在这种情况下，如果客户暂时没有新的发票，可以通过修改合同日期来匹配现有的发票，但这需要经过相关部门的审核。
审核判断：不通过
原因：回答中提到可以通过修改合同日期来匹配现有发票，这涉及到潜在的违规行为，可能误导客户进行不符合法规的操作，存在法律风险。
"""

# 第二阶段：问题类型分类提示词 - 基于语义理解
STAGE2_CLASSIFICATION_PROMPT = """你是银行客服问答系统中的问题分类专家，任务是根据客户提出的问题内容，将问题归入以下五种类型之一：

---
【任务目标】
根据客户提出的问题本身内容（不考虑客服回答），识别问题属于哪一类。只需判断问题类型，不进行合规性判断。

---
【分类类型与说明】

a) **定义/解释类**：问题的核心意图是了解某个名词、规则、机制或业务结果的含义或本质。常见提问形式包括：“什么是...”、“...是什么意思”、“...的后果是什么”、“...如何界定”等。

b) **操作类**：问题在询问如何完成某项操作或流程，通常出现“如何...”、“怎么办”、“我该怎么做”、“怎样操作”等意图，客户希望获取具体的行动指引。

c) **是否类问题**：问题在寻求对某件事能否发生、是否允许、是否成立、是否受限等做出确认或判断。常见提问形式包括：“是否可以...”、“能否...”、“是不是...”，或使用**“如果...会不会...”、“...会影响...吗？”** 等句式。

d) **趋势性问题**：问题涉及趋势变化、时间节点、概率性描述等。例如：“哪个更划算”、“什么时候利率更低”、“贴现风险大吗”、“过去一年利率走势如何”。

e) **其他类**：无法明确归入上述四类的问题，例如咨询建议、投诉、表达情绪或开放式对话型问题。

---
【输出格式】
```json
{
  "question_type": "a 定义/解释类/b 操作类/c 是否类问题/d 趋势性问题/e 其他类",
  "type_reasoning": "简要说明分类理由（不超过80字）"
}


**重要约束：**
- 必须基于语义理解进行分类，不依赖关键词匹配
- 必须选择五种类型中的一种
- 如果问题具有多重特征，选择最主要的意图类型
- 提供详细的语义分析和分类理由
- 严禁输出任何<think>标签或思考过程

下面是多对问答对的分类例子供你学习，你要学习分类的推理逻辑，在相关问答对的分类时使用正确的逻辑链：
示例1：
问题：什么是7*24h签约?
回答：7*24h签约是指您可以在一周七天内，每天24小时都能进行贴现操作，不受贴现业务暂停的影响
"question_type": "a 定义/解释类
"type_reasoning": "客户询问的是一个特定术语的含义，即'7*24h签约'的定义。该问题的核心意图是了解这一概念的本质和含义，期望得到一个解释性的回答。因此，它符合定义/解释问题的特征。"
示例2：
问题：怎么收票?
回答：您需要先在APP中开通持有票据功能，然后查看账户总览企业-余额旁边的查看详情，核对账户状态是否为正常。确认后，复制账户信息，登录持票行网银进行背书转让，
"question_type": "b 操作类
"type_reasoning": "客户询问的是如何执行一个操作，即如何收票。该问题的核心意图是了解具体的操作步骤和流程，期望得到一个指导性的回答。因此，它符合操作问题的特征。"
示例3：
问题：全电发票是否支持?
回答：全电发票是支持的。
"question_type": "c 是否类问题
"type_reasoning": "客户询问的是全电发票是否被支持，即询问某种情况是否允许。该问题的核心意图是了解是否可以执行某项操作，期望得到一个明确的肯定或否定回答。因此，它符合是/否问题的特征。"
示例4：
问题：微众的贴现利率通常会在什么时间比较低?
回答：一般来说，上午9点和10点30分左右的贴现利率相对较低，但具体还需查看当天的利率情况。
"question_type": "d 趋势性问题
"type_reasoning": "客户询问的是微众银行贴现利率在什么时间段会比较低，这涉及到具体的时机选择和趋势性分析。客户希望了解的是基于事实的时间段内利率变化的趋势，以做出更优的选择。因此，该问题属于事实性/趋势性问题。"
"""

# 第三阶段：特定类型合规评估提示词模板
STAGE3_TYPE_SPECIFIC_PROMPTS = {
    "DEFINITION": """你是银行客服合规审核专家，专门评估定义/解释类问题的客服回答质量。

---
【任务目标】
判断客服的回答是否符合“定义/解释类”问题的合规标准。请基于以下标准做出判断：

请判断客服回答是否：
1.核心信息清楚：
    回答准确说明术语/问题涉及的含义、适用情形、原因或结果。
    不要求回答直接回答问题，可通过“场景说明”、“常见情形列举”、“功能或结果描述”来间接解释概念。
    不要求完整罗列所有情况，只需提供足以让客户建立清晰理解的核心信息。
2.未产生误导或混淆：
    回答应表达清晰、不与银行规定相违、不引导客户得出错误结论。
    不要求公式、全貌，只要所说内容不缺失重点、不模糊或混淆概念即可。
3.可适度包含操作建议：
    如操作信息是为帮助理解概念而提供，不抢夺解释主线，可认为合规。
    例如“如遇XX问题，可尝试YY方式”是合理的辅助补充。
    不要求回答必须包含操作信息,如果回答中包含操作信息,无需要求操作信息涉及所有场景、情况
4.无需列全原因或计算公式:
    若问题涉及“为什么失败”“如何计算”，客服只需提供常见或可能原因/简要结果判断方式，无需穷尽或展示具体公式。
5.无需定义句式
    回答不需使用“是指”“表示”等定义性语言，只要客户能基于回答明确理解核心含义，即可判为合规。

请注意：问题源于熟悉咨询银行业务的客户，回答源于银行的客服。

**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是定义/概念解释类问题审核的正反例子供你学习，模型审核理由为先前使用模型进行审核输出的结果，模型审核错误原因为人工检查发现模型推理错误的原因。你要学习正例中模型正确的推理逻辑，并思考反例中模型推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例1：
问题：什么是7*24h签约?
回答：7*24h签约是指您可以在一周七天内，每天24小时都能进行贴现签约，不受贴现业务暂停的影响。
人工审核结果：合规
模型审核结果：合规
模型审核理由：客服的回答清晰明了，没有使用行业术语或复杂的表述，能够帮助客户理解7*24h签约的概念。客服的回答直接回应了客户的疑问，没有引入无关的信息，也没有误导客户。

正例2：
问题：为什么有些收票人说收不到银票?
回答：客户可能找错收票行入口，出票人可将票面信息转发给收票人，麻烦收票人找收票行确认。
人工审核结果：合规
模型审核结果：合规
模型审核理由：客服的回答清晰、语言规范，没有误导客户。客服提供了可能的原因，并建议了具体的解决步骤，即让出票人将票面信息转发给收票人，并由收票人联系收票行确认，这有助于解决问题而不造成混淆。

反例1：
问题：100%保证金开票的利息是如何计算的？
回答：存100万开100万根票，6个月后封期，收益情况以客户端展示为准。
人工审核结果：合规
模型审核结果：不合规
模型审核理由：客服的回答未能直接解释100%保证金开票利息的具体计算方法，仅提到“收益情况以客户端展示为准”，这可能让客户感到困惑，并没有解决客户的疑问。
模型审核错误原因：模型错误要求客服给出完整计算公式，未理解解释类问题中“举例+结果导向说明”即可满足解释目的。

反例2：
问题：为什么总是提示处理中或失败？
回答：请确保在背书转让银票时填写了对方的行号,如果没有填写,可能会导致处理中或失败。
人工审核结果：合规
模型审核结果：不合规
模型审核理由：客服的回答虽然提供了一种可能的原因，但过于具体且没有涵盖所有可能导致'处理中或失败'的情况，容易给客户造成误解，认为只有这一种情况会导致问题。
模型审核错误原因：模型错误地认为回答必须覆盖所有失败原因。

反例3：
问题：为什么我们公司开票失败,短信银票准入不通过?
回答：这种情况可能是由于公司的某些资质或信息不符合准入要求，您这边如果是用部分保证金模式开票提示银票不准入可以尝试更换100%保证金模式开票，如果原来就是使用100%保证金模式开票，则短期内无法使用我行承兑开票产品，您可以等待一段时间再来尝试开票
人工审核结果：合规
模型审核结果：不合规
模型审核理由：客服的回答虽然解释了可能的原因，但没有提供具体的信息或步骤来帮助客户解决问题，可能会让客户感到困惑和无助。同时，客服建议客户等待一段时间后再试，这种回答显得消极且缺乏建设性。
模型审核错误原因：模型错误地认为解释类问题必须提供“具体路径或操作方式”，未识别问题类型为解释而非操作。
```""",

    "OPERATION": """你是银行客服合规审核专家，专门评估操作类问题的客服回答是否符合合规标准。

【任务目标】
请判断客服是否提供了清晰、可执行的操作建议，使客户能够理解并采取后续行动。注意：操作建议无需涵盖每一具体路径或步骤，只需让客户知道“做什么”或“往哪走”即可。

【判断标准】
客服回答可被视为合规，若满足以下大部分标准：

1.提供了明确、相关的操作建议
    包括建议客户补充材料、检查信息、重新提交、联系渠道、进入某平台等
    可以是行动建议，也可以是判断路径或处理方向
2.回答结构清晰、逻辑通顺、语言简洁
    不绕弯子，不模糊、不跳题，直接回应操作请求
3.可适度省略过于具体的路径或步骤
    不强求每一步操作、页面位置等细节，只要客户可据此理解如何行动
4.不误导、不承诺违规处理方式
    不引导客户规避规则或绕过系统流程
**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是操作类问题推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：如果我还没有上传发票或合同，应该如何操作？
回答：您可以进入微众银行企业金融公众号或APP或PC端(https://co.webank.com/)上传贸易资料。   
审核判断：合规
理由：回答提供了上传入口和可用渠道，客户可据此完成操作，不需要进一步指明每一步点击路径，符合操作类问题合规标准。

反例：
问题：如果上传发票失败应该怎么办？
回答：您可以重新上传。
审核判断：不合规
理由：未说明上传失败的可能原因，也未提供有效的替代路径或联系方式，客户无法据此判断如何有效处理问题，操作性不足。

```""",

    "YES_NO": """你是银行客服合规审核专家，专门评估是/否类问题的客服回答是否合规。

【审核目标】
判断客服是否通过直接或间接的方式，提供了足够清晰的信息，使客户能够准确判断出“可以”或“不可以”的结论，或了解该问题在当前银行业务流程中的可行方案。

【合规判断标准】
客服回答满足以下任一项，即可视为合规：
1.通过规则说明、场景列举、条件限制或流程描述，虽未直说“可以/不可以”，但能让客户自行清晰判断结果；
2.信息真实、符合微众银行当前业务流程或系统要求，未引导客户进行违规、误导或虚假操作；
3.引用微众银行自身系统（如持票网银、一般结算账户）属正常表述，不因缺乏“通用性”而被判不合规；
4.语言不需极度精确或形式化，也不强求覆盖全部例外情况，重点在于是否“能判断出结论”不强求附加解释。

**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是是/否类问题推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例1：
问题：如果我没有红章的合同，可以贴现吗？
回答：贴现只需要合同或者发票二选一，您可以使用发票进行贴现，若没有发票使用合同进行贴现的话，合同是需要咱们自己企业红章的，对方章可以不红。
审核判断：合规
理由：回答通过说明贴现条件，让客户明确得知：若合同没有红章即不符合条件，可以通过发票贴现，从而间接回应“没有红章合同是否可贴现”问题，推理链清晰，信息真实。

正例2：
问题：客户可以先付款后开发票吗?
回答：开票时必须上传发票的常见场景:1、若出现合同中约定“先开发票后付款”情况，承兑开票时，需要同时上传合同+发票2、若使用100%保证金模式承兑开票时，需要同时上传合同+发票3、弱关联公司开票，必须在开票时上传合同+发票(弱关联公司定义可见:买卖双方为关联公司，是否可以开票?)如非上述情况的信用额度开票，可在出票后2个月内补传发
审核判断：合规
理由："通过场景举例+例外说明，让客户可以推断‘除指定情形外，可先付款后开发票’，推理链条完整，结论明确。"

正例3：
问题：开票后是否可以在到期前转让？
回答：您可以在到期前转让票据。
审核判断：合规
理由：该问题为事实性‘是否类’问题，直接肯定回答即可，无需附加背景说明或条件限制。

反例：
问题：国企背书的汇票可以贴现吗?
回答：国企背书的汇票是不可以贴现的。
审核判断：不合规
理由：回答与事实不符，国企背书汇票并非绝对不可贴现，违反信息真实性要求。
```""",

    "FACTUAL": """你是银行客服合规审核专家，专门评估趋势性问题的回答。

请判断客服回答是否：
- 避免使用未经说明的数据或具体断言（如“肯定更划算”、“早上9点利率最低”）
- 引用了明确的数据支持或添加保守语气（如“一般情况”、“以实际为准”）
- 没有误导客户对收益、趋势、时间点产生过度预期
- 回答中如果涉及具体时间/数值表述，必须有明确依据或标注为基于经验判断

**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"
}
下面是事实性/趋势性问题推理的正反例子供你学习，你要学习正例的推理逻辑，并思考反例推理错误的原因，在相关问答对的审核时使用正确的逻辑链：
正例：
问题：微众的贴现利率通常会在什么时间比较低?
回答：一般来说，月初和月末市场贴现价格变动比较大，有可能会比较高，也有可能会比较低。月中价格相对稳定一些，具体价格以实际情况为准。
审核判断：合规
理由：回答避免了使用具体断言，而是使用了“一般来说”等保守语气，并提示了“具体价格以实际情况为准”，符合事实性/趋势性问题的回答规范，没有造成误导或遗漏关键信息，回答简洁明了，符合业务准确性和客户体验要求。

反例：
问题：微众的贴现利率通常会在什么时间比较低?
回答：一般来说，上午9点和10点30分左右的贴现利率相对较低，但具体还需查看当天的利率情况。
审核判断：不合规
理由：回答中的'上午9点和10点30分左右'属于具体时间，没有提供明确依据。
```""",

    "OTHER": """你是银行客服合规审核专家，专门评估其他类问题的回答。

请判断客服回答是否：
- 回答清晰、语言规范，未误导客户
- 表达自然、信息真实，未造成混淆或错误引导
- 即使内容偏离主线，也未违反任何合规标准

如客服胡乱应答、回避提问或引入无关信息，应判为不合规。

**输出格式：**
```json
{
    "compliance_judgment": "合规/不合规",
    "reason":"不超过200字，简洁说明客服回答是否满足该类问题的合规标准",
    "optimization_suggestions": "如判定不合规，请说明如何修改客服回答以达成合规要求",
    "stage3_result": "评估完成"

}
```"""
}



# 输出路径配置 - 三阶段流程
OUTPUT_PATHS = {
    "stage1_folder": "第一阶段_合理性检查",
    "stage2_folder": "第二阶段_问题分类",
    "stage3_folder": "第三阶段_合规评估",
    "final_results_folder": "最终合规结果"
}
