#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个问答对多阶段合规评估测试脚本
与multi_stage_compliance_evaluator.py的审核流程保持完全一致
"""

import openai
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
from config import API_CONFIG, PATH_CONFIG, MODEL_CONFIG
from multi_stage_config import (
    MULTI_STAGE_CONFIG, QUESTION_TYPES,
    STAGE1_REASONABLENESS_PROMPT, STAGE2_CLASSIFICATION_PROMPT,
    STAGE3_TYPE_SPECIFIC_PROMPTS,
    OUTPUT_PATHS
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('single_qa_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class SingleQAMultiStageEvaluator:
    """单个问答对多阶段合规评估器"""

    def __init__(self, api_key: str = None, base_url: str = None):
        """初始化评估器"""
        self.client = openai.OpenAI(
            base_url=base_url or API_CONFIG["base_url"],
            api_key=api_key or API_CONFIG["api_key"]
        )
        self.model = API_CONFIG["model"]
        self.model_config = MODEL_CONFIG
        self.multi_stage_config = MULTI_STAGE_CONFIG

        # 创建输出目录
        self._create_output_directories()

    def _create_output_directories(self):
        """创建输出目录"""
        base_folder = "单个问答对测试结果"
        os.makedirs(base_folder, exist_ok=True)
        os.makedirs(os.path.join(base_folder, "中间结果"), exist_ok=True)
        self.output_folder = base_folder

    def _call_model(self, prompt: str, user_content: str, max_retries: int = 3) -> Optional[str]:
        """调用模型API，包含重试机制（与原系统完全一致）"""
        for attempt in range(max_retries):
            try:
                messages = [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": user_content}
                ]

                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.model_config["temperature"],
                    max_tokens=self.model_config["max_tokens"]
                )

                return response.choices[0].message.content

            except Exception as e:
                logging.warning(f"API调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    logging.error(f"API调用最终失败: {e}")
                    return None

    def _parse_json_response(self, response: str) -> Optional[Dict]:
        """解析JSON响应，处理可能的格式问题（与原系统完全一致）"""
        import re

        # 清理响应文本
        cleaned_response = response.strip()

        # 策略1：尝试直接解析
        try:
            return json.loads(cleaned_response)
        except json.JSONDecodeError:
            pass

        # 策略2：提取最外层的JSON代码块
        try:
            json_match = re.search(r'```json\s*(.*?)\s*```', cleaned_response, re.DOTALL)
            if json_match:
                json_content = json_match.group(1).strip()
                return json.loads(json_content)
        except json.JSONDecodeError:
            pass

        # 策略3：提取任何代码块（不限于json标记）
        try:
            code_block_match = re.search(r'```[a-zA-Z]*\s*(.*?)\s*```', cleaned_response, re.DOTALL)
            if code_block_match:
                json_content = code_block_match.group(1).strip()
                return json.loads(json_content)
        except json.JSONDecodeError:
            pass

        # 策略4：查找第一个完整的JSON对象
        try:
            start_idx = cleaned_response.find('{')
            if start_idx != -1:
                brace_count = 0
                end_idx = start_idx
                for i, char in enumerate(cleaned_response[start_idx:], start_idx):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i + 1
                            break

                if brace_count == 0:
                    json_content = cleaned_response[start_idx:end_idx]
                    return json.loads(json_content)
        except json.JSONDecodeError:
            pass

        # 策略5：尝试修复常见的JSON格式问题
        try:
            # 移除可能的前后缀文本
            lines = cleaned_response.split('\n')
            json_lines = []
            in_json = False
            brace_count = 0

            for line in lines:
                if '{' in line and not in_json:
                    in_json = True
                    json_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                elif in_json:
                    json_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                    if brace_count <= 0:
                        break

            if json_lines:
                json_content = '\n'.join(json_lines)
                return json.loads(json_content)

        except json.JSONDecodeError:
            pass

        # 记录详细的错误信息
        logging.error(f"所有JSON解析策略都失败了")
        logging.error(f"响应长度: {len(response)}")
        logging.error(f"响应前500字符: {response[:500]}")
        logging.error(f"响应后100字符: {response[-100:]}")

        return None

    def _extract_stage1_failure_reason(self, raw_response: str, stage1_result: Dict[str, Any]) -> str:
        """从第一阶段的原始响应中提取真正的不合规理由（与原系统完全一致）"""
        try:
            # 如果raw_response中包含JSON之外的解释文本，提取它
            import re

            # 查找JSON代码块之后的文本
            json_pattern = r'```json.*?```'
            match = re.search(json_pattern, raw_response, re.DOTALL)

            if match:
                # 获取JSON代码块之后的文本
                after_json = raw_response[match.end():].strip()
                if after_json:
                    # 清理文本，移除多余的换行和空格
                    cleaned_reason = re.sub(r'\s+', ' ', after_json).strip()
                    if cleaned_reason and len(cleaned_reason) > 10:  # 确保有实质内容
                        return cleaned_reason

            # 如果没有找到JSON之后的解释，检查reasoning字段是否包含实际的不合规理由
            reasoning = stage1_result.get('reasoning', '')
            reasonableness = stage1_result.get('reasonableness_judgment', '')

            # 如果reasoning字段描述的是问题的合理性而不是不合规的原因，
            # 说明AI的回答有问题，我们需要提供一个通用的说明
            if reasonableness == "合理" and "实际问题" in reasoning:
                return "虽然问题本身合理，但客服回答存在合规风险或不当引导，未通过第一阶段审核。"

            # 否则使用原始的reasoning
            return reasoning if reasoning else "未提供具体理由"

        except Exception as e:
            logging.warning(f"提取第一阶段失败原因时出错: {e}")
            return stage1_result.get('reasoning', '未提供理由')

    def _save_stage_result(self, stage: str, file_id: str, result: Dict[str, Any]):
        """保存阶段结果"""
        if self.multi_stage_config.get("save_intermediate_results", True):
            stage_folder = os.path.join(self.output_folder, "中间结果", stage)
            os.makedirs(stage_folder, exist_ok=True)

            output_file = os.path.join(stage_folder, f"{file_id}_{stage}_result.json")
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logging.error(f"保存{stage}阶段结果失败: {e}")

    def stage1_reasonableness_check(self, qa_data: Dict[str, Any]) -> Dict[str, Any]:
        """第一阶段：合理性检查（与原系统完全一致）"""
        logging.info("执行第一阶段：合理性检查")

        question = qa_data.get("问题", "")
        answer = qa_data.get("回答", "")

        user_content = f"""
【客户问题】: {question}
【客服回答】: {answer}

        """.strip()

        response = self._call_model(STAGE1_REASONABLENESS_PROMPT, user_content)
        if not response:
            return {"stage1_result": "失败", "error": "API调用失败"}

        parsed_result = self._parse_json_response(response)
        if not parsed_result:
            return {"stage1_result": "失败", "error": "响应解析失败", "raw_response": response}

        # 添加原始响应用于调试
        parsed_result["raw_response"] = response
        parsed_result["stage"] = "stage1_reasonableness"

        return parsed_result

    def stage2_question_classification(self, qa_data: Dict[str, Any]) -> Dict[str, Any]:
        """第二阶段：基于语义理解的问题类型分类（与原系统完全一致）"""
        logging.info("执行第二阶段：基于语义理解的问题类型分类")

        question = qa_data.get("问题", "")
        answer = qa_data.get("回答", "")
        category = qa_data.get("类别", "")

        user_content = f"""
【业务类别】: {category}
【客户问题】: {question}
【客服回答】: {answer}


        """.strip()

        response = self._call_model(STAGE2_CLASSIFICATION_PROMPT, user_content)
        if not response:
            return {"stage2_result": "失败", "error": "API调用失败"}

        parsed_result = self._parse_json_response(response)
        if not parsed_result:
            return {"stage2_result": "失败", "error": "响应解析失败", "raw_response": response}

        # 解析问题类型，从新格式中提取类型标识符
        question_type_raw = parsed_result.get("question_type", "")

        # 映射新格式的问题类型到内部标识符
        type_mapping = {
            "a 定义/解释类": "DEFINITION",
            "b 操作类": "OPERATION",
            "c 是否类问题": "YES_NO",
            "d 趋势性问题": "FACTUAL",
            "e 其他类": "OTHER"
        }

        question_type = None
        for key, value in type_mapping.items():
            if question_type_raw.startswith(key):
                question_type = value
                break

        if not question_type or question_type not in QUESTION_TYPES:
            logging.warning(f"无效的问题类型: {question_type_raw}")
            question_type = "OTHER"  # 默认为其他类问题

        # 更新解析结果
        parsed_result["question_type"] = question_type
        parsed_result["question_type_raw"] = question_type_raw
        parsed_result["classification_method"] = "semantic_understanding"
        parsed_result["raw_response"] = response
        parsed_result["stage"] = "stage2_classification"

        # 记录分类详情
        logging.info(f"问题分类结果: {question_type} ({question_type_raw})")

        return parsed_result

    def stage3_type_specific_evaluation(self, qa_data: Dict[str, Any], question_type: str) -> Dict[str, Any]:
        """第三阶段：特定类型合规评估（与原系统完全一致）"""
        logging.info(f"执行第三阶段：{question_type}类型合规评估")

        if question_type not in STAGE3_TYPE_SPECIFIC_PROMPTS:
            logging.error(f"未找到问题类型 {question_type} 的评估提示词")
            return {"stage3_result": "失败", "error": f"未支持的问题类型: {question_type}"}

        question = qa_data.get("问题", "")
        answer = qa_data.get("回答", "")
        category = qa_data.get("类别", "")

        user_content = f"""
【业务类别】: {category}
【问题类型】: {QUESTION_TYPES[question_type]['name']}
【客户问题】: {question}
【客服回答】: {answer}

请根据该问题类型的特定标准进行合规评估。
        """.strip()

        prompt = STAGE3_TYPE_SPECIFIC_PROMPTS[question_type]
        response = self._call_model(prompt, user_content)

        if not response:
            return {"stage3_result": "失败", "error": "API调用失败"}

        parsed_result = self._parse_json_response(response)
        if not parsed_result:
            return {"stage3_result": "失败", "error": "响应解析失败", "raw_response": response}

        parsed_result["raw_response"] = response
        parsed_result["stage"] = "stage3_type_specific"
        parsed_result["evaluated_type"] = question_type

        return parsed_result

    def process_single_qa(self, qa_data: Dict[str, Any], file_id: str) -> Dict[str, Any]:
        """处理单个问答对的完整三阶段流程（与原系统完全一致）"""
        logging.info(f"开始处理问答对: {file_id}")

        # 初始化结果结构，参考现有格式
        final_result = {
            "文件名": f"{file_id}.json",  # 如：001.json
            "原始数据": qa_data,
            "评估时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "合规评估结果": "",
            "评估状态": "进行中",
            "多阶段结果": {},
            "不合规阶段": None  # 新增：记录在哪个阶段判定为不合规
        }

        try:
            # 第一阶段：合理性检查
            stage1_result = self.stage1_reasonableness_check(qa_data)
            final_result["多阶段结果"]["stage1"] = stage1_result
            self._save_stage_result("stage1", file_id, stage1_result)

            # 检查第一阶段是否有错误
            if stage1_result.get("error"):
                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第一阶段处理失败: {stage1_result['error']}"
                final_result["评估状态"] = "第一阶段失败"
                final_result["不合规阶段"] = "第一阶段"
                return final_result

            # 检查第一阶段是否通过（主要看stage1_result）
            if stage1_result.get("stage1_result") == "不通过":
                # 从原始响应中提取真正的不合规理由
                raw_response = stage1_result.get('raw_response', '')

                # 尝试从raw_response中提取不通过的真正原因
                actual_reason = self._extract_stage1_failure_reason(raw_response, stage1_result)

                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第一阶段合理性检查不通过: {actual_reason}"
                final_result["评估状态"] = "第一阶段终止"
                final_result["不合规阶段"] = "第一阶段"
                return final_result

            time.sleep(self.multi_stage_config["stage_delay"])

            # 第二阶段：问题类型分类
            stage2_result = self.stage2_question_classification(qa_data)
            final_result["多阶段结果"]["stage2"] = stage2_result
            self._save_stage_result("stage2", file_id, stage2_result)

            # 检查第二阶段是否有错误
            if stage2_result.get("error"):
                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第二阶段处理失败: {stage2_result['error']}"
                final_result["评估状态"] = "第二阶段失败"
                final_result["不合规阶段"] = "第二阶段"
                return final_result

            question_type = stage2_result.get("question_type", "OPERATION")
            time.sleep(self.multi_stage_config["stage_delay"])

            # 第三阶段：特定类型合规评估（最终阶段）
            stage3_result = self.stage3_type_specific_evaluation(qa_data, question_type)
            final_result["多阶段结果"]["stage3"] = stage3_result
            self._save_stage_result("stage3", file_id, stage3_result)

            # 检查第三阶段是否有错误
            if stage3_result.get("error"):
                final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n第三阶段处理失败: {stage3_result['error']}"
                final_result["评估状态"] = "第三阶段失败"
                final_result["不合规阶段"] = "第三阶段"
                return final_result

            # 从第三阶段直接提取最终结果
            compliance_judgment = stage3_result.get("compliance_judgment", "不合规")
            reason = stage3_result.get("reason", "基于第三阶段合规评估结果")

            # 构建合规评估结果，参考现有格式
            final_result["合规评估结果"] = f"**最终合规判断：** {compliance_judgment}\n\n**判断理由：**\n{reason}"

            # 如果有优化建议，添加到结果中
            if stage3_result.get("optimization_suggestions"):
                final_result["合规评估结果"] += f"\n\n**优化建议：**\n{stage3_result['optimization_suggestions']}"

            # 如果判断为不合规，记录不合规阶段
            if compliance_judgment == "不合规":
                final_result["不合规阶段"] = "第三阶段"

            # 添加评估详情到多阶段结果中
            final_result["多阶段结果"]["评估详情"] = {
                "问题类型": question_type,
                "类型名称": QUESTION_TYPES.get(question_type, {}).get('name', '未知'),
                "合规评估": stage3_result
            }

            final_result["评估状态"] = "成功"

        except Exception as e:
            logging.error(f"处理问答对 {file_id} 时出错: {e}")
            final_result["评估状态"] = "失败"
            final_result["合规评估结果"] = f"**最终合规判断：** 不合规\n\n**判断理由：**\n处理过程中出现异常: {str(e)}"
            final_result["不合规阶段"] = "处理异常"

        return final_result

    def save_result(self, result: Dict[str, Any], file_id: str):
        """保存测试结果"""
        output_file = os.path.join(self.output_folder, f"{file_id}_single_qa_test_result.json")
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logging.info(f"结果已保存到: {output_file}")
        except Exception as e:
            logging.error(f"保存结果失败: {e}")

    def test_qa_from_input(self) -> Dict[str, Any]:
        """从用户输入测试问答对"""
        print("\n=== 单个问答对多阶段合规评估测试 ===")
        print("请输入问答对信息：")

        # 获取用户输入
        category = input("业务类别: ").strip()
        question = input("客户问题: ").strip()
        answer = input("客服回答: ").strip()

        if not question or not answer:
            print("错误：问题和回答不能为空！")
            return None

        # 构建问答对数据
        qa_data = {
            "类别": category,
            "问题": question,
            "回答": answer
        }

        # 生成测试ID
        test_id = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        print(f"\n开始评估问答对 (ID: {test_id})...")

        # 执行评估
        result = self.process_single_qa(qa_data, test_id)

        # 保存结果
        self.save_result(result, test_id)

        return result

    def test_qa_from_file(self, file_path: str) -> Dict[str, Any]:
        """从文件测试问答对"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                qa_data = json.load(f)

            # 提取文件名作为ID
            file_id = os.path.splitext(os.path.basename(file_path))[0]

            print(f"\n开始评估文件: {file_path}")

            # 执行评估
            result = self.process_single_qa(qa_data, file_id)

            # 保存结果
            self.save_result(result, file_id)

            return result

        except Exception as e:
            logging.error(f"从文件加载问答对失败: {e}")
            return None

    def display_result(self, result: Dict[str, Any]):
        """显示评估结果"""
        if not result:
            print("没有结果可显示")
            return

        print("\n" + "="*60)
        print("评估结果")
        print("="*60)

        print(f"文件名: {result.get('文件名', 'N/A')}")
        print(f"评估时间: {result.get('评估时间', 'N/A')}")
        print(f"评估状态: {result.get('评估状态', 'N/A')}")

        if result.get('不合规阶段'):
            print(f"不合规阶段: {result.get('不合规阶段')}")

        print("\n原始数据:")
        original_data = result.get('原始数据', {})
        print(f"  类别: {original_data.get('类别', 'N/A')}")
        print(f"  问题: {original_data.get('问题', 'N/A')}")
        print(f"  回答: {original_data.get('回答', 'N/A')}")

        print(f"\n{result.get('合规评估结果', 'N/A')}")

        # 显示阶段详情
        multi_stage_results = result.get('多阶段结果', {})
        if multi_stage_results:
            print("\n阶段详情:")

            # 第一阶段
            stage1 = multi_stage_results.get('stage1', {})
            if stage1:
                print(f"  第一阶段 (合理性检查): {stage1.get('stage1_result', 'N/A')}")

            # 第二阶段
            stage2 = multi_stage_results.get('stage2', {})
            if stage2:
                question_type = stage2.get('question_type_raw', stage2.get('question_type', 'N/A'))
                print(f"  第二阶段 (问题分类): {question_type}")

            # 第三阶段
            stage3 = multi_stage_results.get('stage3', {})
            if stage3:
                compliance = stage3.get('compliance_judgment', 'N/A')
                print(f"  第三阶段 (合规评估): {compliance}")

        print("="*60)


def main():
    """主函数 - 从完整问答对(优化后)文件夹中测试问答对"""

    # 完整问答对文件夹路径
    qa_folder = "完整问答对(优化后)"

    # 检查文件夹是否存在
    if not os.path.exists(qa_folder):
        print(f"错误：找不到文件夹 '{qa_folder}'")
        print("请确保该文件夹存在并包含问答对JSON文件")
        return

    # 获取所有JSON文件
    json_files = [f for f in os.listdir(qa_folder) if f.endswith('.json')]
    if not json_files:
        print(f"错误：在文件夹 '{qa_folder}' 中没有找到JSON文件")
        return

    # 按文件名排序
    json_files.sort()

    # 创建评估器
    evaluator = SingleQAMultiStageEvaluator()

    print("单个问答对多阶段合规评估测试")
    print("与 multi_stage_compliance_evaluator.py 审核流程完全一致")
    print(f"从文件夹 '{qa_folder}' 中读取问答对数据")
    print(f"找到 {len(json_files)} 个问答对文件")

    # 询问用户要测试哪个文件
    print("\n请选择要测试的文件：")
    print("1. 测试单个文件（输入文件名，如：001.json）")
    print("2. 测试前N个文件（输入数字，如：5）")
    print("3. 测试所有文件")

    choice = input("\n请输入选择: ").strip()

    files_to_test = []

    if choice.endswith('.json'):
        # 测试单个文件
        if choice in json_files:
            files_to_test = [choice]
        else:
            print(f"错误：文件 '{choice}' 不存在")
            return
    elif choice.isdigit():
        # 测试前N个文件
        n = int(choice)
        if n > 0:
            files_to_test = json_files[:min(n, len(json_files))]
        else:
            print("错误：请输入大于0的数字")
            return
    elif choice == "3":
        # 测试所有文件
        files_to_test = json_files
    else:
        print("错误：无效的选择")
        return

    print(f"\n准备测试 {len(files_to_test)} 个问答对...")
    print("="*60)

    # 逐个测试问答对
    for i, filename in enumerate(files_to_test, 1):
        file_path = os.path.join(qa_folder, filename)
        file_id = os.path.splitext(filename)[0]  # 去掉.json扩展名

        print(f"\n[{i}/{len(files_to_test)}] 开始测试文件: {filename}")

        try:
            # 读取问答对数据
            with open(file_path, 'r', encoding='utf-8') as f:
                qa_data = json.load(f)

            print(f"类别: {qa_data.get('类别', 'N/A')}")
            print(f"问题: {qa_data.get('问题', 'N/A')}")
            print(f"回答: {qa_data.get('回答', 'N/A')}")
            print(f"分类: {qa_data.get('分类', 'N/A')}")
            print(f"是否可用: {qa_data.get('是否可用', 'N/A')}")
            print("-" * 40)

            # 执行评估
            result = evaluator.process_single_qa(qa_data, file_id)

            # 保存结果
            evaluator.save_result(result, file_id)

            # 显示结果
            evaluator.display_result(result)

        except Exception as e:
            logging.error(f"测试文件 {filename} 时出错: {e}")
            print(f"❌ 测试失败: {e}")

        print("="*60)

    print(f"\n✅ 所有测试完成！结果已保存到 '单个问答对测试结果' 文件夹中")


if __name__ == "__main__":
    main()
