2025-08-08 16:21:30,844 - INFO - 开始处理问答对: 009
2025-08-08 16:21:30,845 - INFO - 执行第一阶段：合理性检查
2025-08-08 16:21:35,468 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:21:36,474 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 16:21:41,145 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:21:41,147 - WARNING - 无效的问题类型: a 定义/解释类
2025-08-08 16:21:41,148 - INFO - 问题分类结果: OTHER (a 定义/解释类)
2025-08-08 16:21:42,149 - ERROR - 处理问答对 009 时出错: 'SingleQAMultiStageEvaluator' object has no attribute 'stage3_type_specific_evaluation'
2025-08-08 16:21:42,152 - INFO - 结果已保存到: 单个问答对测试结果\009_single_qa_test_result.json
2025-08-08 16:46:59,845 - INFO - 开始处理问答对: test_001
2025-08-08 16:46:59,845 - INFO - 执行第一阶段：合理性检查
2025-08-08 16:47:02,981 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:47:03,989 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 16:47:07,233 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:47:07,235 - WARNING - 无效的问题类型: a 定义/解释类
2025-08-08 16:47:07,235 - INFO - 问题分类结果: OTHER (a 定义/解释类)
2025-08-08 16:47:08,237 - INFO - 执行第三阶段：OTHER类型合规评估
2025-08-08 16:47:11,093 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:47:11,096 - INFO - 结果已保存到: 单个问答对测试结果\test_001_single_qa_test_result.json
2025-08-08 16:47:11,100 - INFO - 开始处理问答对: test_002
2025-08-08 16:47:11,100 - INFO - 执行第一阶段：合理性检查
2025-08-08 16:47:13,687 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:47:14,690 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 16:50:26,895 - INFO - 开始处理问答对: 009
2025-08-08 16:50:26,896 - INFO - 执行第一阶段：合理性检查
2025-08-08 16:50:30,509 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:50:31,514 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 16:50:35,687 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:50:35,688 - WARNING - 无效的问题类型: a 定义/解释类
2025-08-08 16:50:35,689 - INFO - 问题分类结果: OTHER (a 定义/解释类)
2025-08-08 16:50:36,691 - INFO - 执行第三阶段：OTHER类型合规评估
2025-08-08 16:50:40,329 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:50:40,333 - INFO - 结果已保存到: 单个问答对测试结果\009_single_qa_test_result.json
2025-08-08 16:54:12,453 - INFO - 开始处理问答对: 009
2025-08-08 16:54:12,454 - INFO - 执行第一阶段：合理性检查
2025-08-08 16:54:15,124 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:54:16,129 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 16:54:19,716 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:54:19,718 - WARNING - 无效的问题类型: a 定义/解释类
2025-08-08 16:54:19,718 - INFO - 问题分类结果: OTHER (a 定义/解释类)
2025-08-08 16:54:20,720 - INFO - 执行第三阶段：OTHER类型合规评估
2025-08-08 16:54:25,748 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:54:25,750 - INFO - 结果已保存到: 单个问答对测试结果\009_single_qa_test_result.json
2025-08-08 16:57:11,893 - INFO - 开始处理问答对: 009
2025-08-08 16:57:11,893 - INFO - 执行第一阶段：合理性检查
2025-08-08 16:57:15,970 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:57:16,972 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 16:57:19,275 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:57:19,276 - WARNING - 无效的问题类型: a 定义/解释类
2025-08-08 16:57:19,276 - INFO - 问题分类结果: OTHER (a 定义/解释类)
2025-08-08 16:57:20,277 - INFO - 执行第三阶段：OTHER类型合规评估
2025-08-08 16:57:25,036 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:57:25,043 - INFO - 结果已保存到: 单个问答对测试结果\009_single_qa_test_result.json
2025-08-08 17:01:00,484 - INFO - 开始处理问答对: 009
2025-08-08 17:01:11,700 - INFO - 执行第一阶段：合理性检查
2025-08-08 17:02:00,241 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 17:02:34,563 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 17:03:48,414 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 17:04:28,444 - WARNING - 无效的问题类型: a 定义/解释类
2025-08-08 18:15:40,740 - INFO - 开始处理问答对: 009
2025-08-08 18:16:26,124 - INFO - 执行第一阶段：合理性检查
2025-08-08 18:16:30,341 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:16:49,596 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 18:16:59,065 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:22:50,419 - INFO - 开始处理问答对: 009
2025-08-08 18:22:53,508 - INFO - 执行第一阶段：合理性检查
2025-08-08 18:22:58,746 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:24:21,962 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 18:24:25,043 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:24:25,047 - INFO - 问题分类结果: DEFINITION (a 定义/解释类)
2025-08-08 18:25:07,924 - INFO - 执行第三阶段：DEFINITION类型合规评估
2025-08-08 18:25:50,411 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:26:23,557 - INFO - 结果已保存到: 单个问答对测试结果\009_single_qa_test_result.json
2025-08-08 18:26:45,165 - INFO - 开始处理问答对: 009
2025-08-08 18:26:45,166 - INFO - 执行第一阶段：合理性检查
2025-08-08 18:26:48,613 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:26:49,617 - INFO - 执行第二阶段：基于语义理解的问题类型分类
2025-08-08 18:26:52,008 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:26:52,009 - INFO - 问题分类结果: DEFINITION (a 定义/解释类)
2025-08-08 18:26:53,010 - INFO - 执行第三阶段：DEFINITION类型合规评估
2025-08-08 18:26:57,715 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 18:26:57,718 - INFO - 结果已保存到: 单个问答对测试结果\009_single_qa_test_result.json
